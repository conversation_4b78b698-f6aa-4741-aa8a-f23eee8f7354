# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

"""
ModernBERT Inference Pipeline

A comprehensive inference system for ModernBERT models supporting:
- MLM (Masked Language Modeling) inference for mask-fill tasks
- Sentence embedding generation for text representation
- Automatic padding/unpadding configuration based on training config
- Support for various attention mechanisms (RoPE, CABLE, ROTALI)
"""

from inference.inference import ModernBERTInference
from inference.pipelines.mlm_pipeline import MLMPipeline
from inference.pipelines.embedding_pipeline import EmbeddingPipeline

__version__ = "1.0.0"
__all__ = ["ModernBERTInference", "MLMPipeline", "EmbeddingPipeline"] 