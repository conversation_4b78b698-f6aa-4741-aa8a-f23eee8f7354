from .attention import (
    Bert<PERSON><PERSON>biUnpad<PERSON>ttention,
    Bert<PERSON>libiUnpadSelfAttention,
    BertSelfOutput,
    FlexBertPaddedAttention,
    FlexBertUnpadAttention,
)
# First, check if Triton is available
import importlib.util
TRITON_AVAILABLE = importlib.util.find_spec("triton") is not None

# Import standard implementations
from .cable import (
    FlexBertPaddedCableAttention,
    FlexBertUnpadCableAttention,
)
from .rotali_attention import (
    FlexBertRotaliAttention,
    FlexBertUnpadRotaliAttention,
)
from .fourier_layer import (
    FlexBertFourierLayer,
    FlexBertUnpadFourierLayer,
)

# Import Triton implementations if available
if TRITON_AVAILABLE:
    try:
        from .cable_triton import (
            FlexBertPaddedCableAttentionTriton,
            FlexBertUnpadCableAttentionTriton,
        )
    except ImportError as e:
        print(f"DEBUG: Failed to import Triton modules: {e}")
        TRITON_AVAILABLE = False

from .embeddings import (
    Bert<PERSON>libiEmbeddings,
    FlexBertAbsoluteEmbeddings,
    FlexBertSansPositionEmbeddings,
)
from .layers import (
    Bert<PERSON><PERSON>biEncoder,
    BertAlibiLayer,
    BertResidualGLU,
    FlexBertPaddedPreNormLayer,
    FlexBertPaddedPostNormLayer,
    FlexBertUnpadPostNormLayer,
    FlexBertUnpadPreNormLayer,
)
from .model import (
    BertLMPredictionHead,
    BertModel,
    BertForMaskedLM,
    BertForSequenceClassification,
    BertForMultipleChoice,
    BertOnlyMLMHead,
    BertOnlyNSPHead,
    BertPooler,
    BertPredictionHeadTransform,
    FlexBertModel,
    FlexBertForMaskedLM,
    FlexBertForSequenceClassification,
    FlexBertForMultipleChoice,
)


__all__ = [
    "BertAlibiEmbeddings",
    "BertAlibiEncoder",
    "BertForMaskedLM",
    "BertForSequenceClassification",
    "BertForMultipleChoice",
    "BertResidualGLU",
    "BertAlibiLayer",
    "BertLMPredictionHead",
    "BertModel",
    "BertOnlyMLMHead",
    "BertOnlyNSPHead",
    "BertPooler",
    "BertPredictionHeadTransform",
    "BertSelfOutput",
    "BertAlibiUnpadAttention",
    "BertAlibiUnpadSelfAttention",
    "FlexBertPaddedAttention",
    "FlexBertUnpadAttention",
    "FlexBertPaddedCableAttention",
    "FlexBertUnpadCableAttention",
    "FlexBertRotaliAttention",
    "FlexBertUnpadRotaliAttention",
    "FlexBertFourierLayer",
    "FlexBertUnpadFourierLayer",
    "FlexBertAbsoluteEmbeddings",
    "FlexBertSansPositionEmbeddings",
    "FlexBertPaddedPreNormLayer",
    "FlexBertPaddedPostNormLayer",
    "FlexBertUnpadPostNormLayer",
    "FlexBertUnpadPreNormLayer",
    "FlexBertModel",
    "FlexBertForMaskedLM",
    "FlexBertForSequenceClassification",
    "FlexBertForMultipleChoice",
]

# Add Triton implementations to __all__ if available
if TRITON_AVAILABLE:
    __all__.extend([
        "FlexBertPaddedCableAttentionTriton",
        "FlexBertUnpadCableAttentionTriton",
    ])