import math
import torch
import torch.nn as nn
from torch.nn import functional as F
from typing import Op<PERSON>, Tuple, Union

from .configuration_bert import FlexBertConfig
from .initialization import ModuleType, init_weights


class FlexBertPaddedCableAttention(nn.Module):
    """Performs multi-headed self attention on a batch of padded sequences using CABLE.

    This module supports two attention implementations:
    1. Flash Attention 2 (if installed), which improves throughput.
    2. PyTorch's scaled_dot_product_attention.

    See `forward` method for additional details.
    """

    def __init__(self, config: FlexBertConfig, layer_id: Optional[int] = None):
        super().__init__()
        self.config = config
        self.layer_id = layer_id

        if config.hidden_size % config.num_attention_heads != 0 and not hasattr(config, "embedding_size"):
            raise ValueError(
                f"The hidden size ({config.hidden_size}) is not a multiple of the number of attention "
                f"heads ({config.num_attention_heads})"
            )

        self.num_attention_heads = config.num_attention_heads
        self.attn_head_size = int(config.hidden_size / config.num_attention_heads)
        self.hidden_size = config.hidden_size
        self.p_dropout = config.attention_probs_dropout_prob

        # key, query, value projections for all heads, but in a batch
        self.Wqkv = nn.Linear(config.hidden_size, 3 * config.hidden_size, bias=config.attn_qkv_bias)

        # biases projections for all heads, but in a batch
        self.h_attn = nn.Linear(config.hidden_size, config.num_attention_heads)

        # output projection
        self.Wo = nn.Linear(config.hidden_size, config.hidden_size, bias=config.attn_out_bias)
        self.out_drop = (
            nn.Dropout(config.attn_out_dropout_prob) if config.attn_out_dropout_prob > 0.0 else nn.Identity()
        )

        self.block_size = config.max_position_embeddings
        self.dtype = torch.float32  # Use consistent dtype

    def _init_weights(self, reset_params: bool = False):
        init_weights(
            self.config,
            self.Wqkv,
            layer_dim=self.config.hidden_size,
            layer_id=None,
            type_of_module=ModuleType.in_module,
        )
        init_weights(
            self.config,
            self.h_attn,
            layer_dim=self.config.hidden_size,
            layer_id=None,
            type_of_module=ModuleType.in_module,
        )
        init_weights(
            self.config,
            self.Wo,
            layer_dim=self.config.hidden_size,
            layer_id=self.layer_id,
            type_of_module=ModuleType.out_module,
        )

    def extra_repr(self) -> str:
        repr = ""
        if hasattr(self, "num_attention_heads"):
            repr += f"num_attention_heads={self.num_attention_heads}"
        if hasattr(self, "attn_head_size"):
            repr += f", attn_head_size={self.attn_head_size}"
        return repr

    def forward(
        self,
        hidden_states: torch.Tensor,
        attn_mask: Optional[torch.Tensor] = None,
    ) -> torch.Tensor:
        """Perform self-attention with CABLE.

        Args:
            hidden_states: (batch, seqlen, dim)
            attn_mask: (batch, seqlen)

        Returns:
            attention: (batch, seqlen, dim)
        """
        B, T, C = hidden_states.size()  # batch size, sequence length, embedding dimensionality

        # extracting query, key, and value vectors
        qkv = self.Wqkv(hidden_states)
        q, k, v = qkv.split(self.hidden_size, dim=2)

        # Relative biases for tokens on each head
        b = torch.cumsum(-F.softplus(self.h_attn(hidden_states)), dim=1).permute(0, 2, 1)  # (B, nh, T)

        k = k.view(B, T, self.num_attention_heads, C // self.num_attention_heads).transpose(1, 2)  # (B, nh, T, hs)
        q = q.view(B, T, self.num_attention_heads, C // self.num_attention_heads).transpose(1, 2)  # (B, nh, T, hs)
        v = v.view(B, T, self.num_attention_heads, C // self.num_attention_heads).transpose(1, 2)  # (B, nh, T, hs)

        # Calculate biases - no causal mask for bidirectional attention
        
        # Ensure biases are always negative by taking the negative absolute difference
        # Bias = (-1) * torch.abs(b.unsqueeze(3) - b.unsqueeze(2))  # (B, nh, T, T)
        Bias = (-1) * torch.log((b.unsqueeze(3) - b.unsqueeze(2))**2 + 1 )  # (B, nh, T, T)

        # Reshape for attention computation
        b_shape = Bias.shape
        bt = Bias.reshape(b_shape[0] * b_shape[1], b_shape[2], b_shape[3])

        q_shape = q.shape
        qt = q.reshape(q_shape[0] * q_shape[1], q_shape[2], q_shape[3])
        k_shape = k.shape
        kt = k.reshape(k_shape[0] * k_shape[1], k_shape[2], k_shape[3])
        kt = kt.transpose(1, 2)

        # Apply attention mask if provided
        if attn_mask is not None:
            # Ensure mask is boolean type
            if attn_mask.dtype != torch.bool:
                attn_mask = attn_mask.to(torch.bool)

            # Expand mask to match attention shape
            expanded_mask = attn_mask[:, None, None, :T].expand(B, self.num_attention_heads, T, T)
            expanded_mask = expanded_mask.reshape(B * self.num_attention_heads, T, T)
            # Set masked positions to -inf
            bt = bt.masked_fill(~expanded_mask, float('-inf'))

        # Use different approaches for training and evaluation
        scale = 1.0 / math.sqrt(C/self.num_attention_heads)

        qt_scaled = qt * scale
        try:
            # baddbmm: beta * input + alpha * (batch1 @ batch2)
            # Here beta=1, input=bt, alpha=1, batch1=qt_scaled, batch2=kt
            att_scores = torch.baddbmm(bt, qt_scaled, kt)
            att_scores = att_scores.reshape(B, self.num_attention_heads, T, T)
        except RuntimeError as e:
            # Fallback to standard matmul if baddbmm fails
            qk = torch.matmul(qt_scaled, kt)  # [B*nh, T, T]
            att_scores = qk + bt
            att_scores = att_scores.reshape(B, self.num_attention_heads, T, T)

        # Apply softmax and dropout with numerical stability
        if not self.training:
            # More stable approach for evaluation
            # First normalize to prevent overflow
            att_scores = att_scores - att_scores.max(dim=-1, keepdim=True)[0]
            # Apply softmax with extra stability measures
            exp_scores = torch.exp(att_scores)
            y = exp_scores / (exp_scores.sum(dim=-1, keepdim=True) + 1e-10)
            # No dropout during evaluation

        else:
            # Standard approach for training
            y = F.softmax(att_scores, dim=-1)
            y = F.dropout(y, p=self.p_dropout, training=self.training)

        # Apply attention to values
        y = y @ v

        # Reshape back
        y = y.transpose(1, 2).contiguous().view(B, T, C)

        # Output projection
        y = self.out_drop(self.Wo(y))

        return y


class FlexBertUnpadCableAttention(nn.Module):
    """Performs multi-headed self attention on a batch of unpadded sequences using CABLE.

    This module supports two attention implementations:
    1. Flash Attention 2 (if installed), which improves throughput.
    2. PyTorch's scaled_dot_product_attention.

    See `forward` method for additional details.
    """

    def __init__(self, config: FlexBertConfig, layer_id: Optional[int] = None):
        super().__init__()
        self.config = config
        self.layer_id = layer_id

        if config.hidden_size % config.num_attention_heads != 0 and not hasattr(config, "embedding_size"):
            raise ValueError(
                f"The hidden size ({config.hidden_size}) is not a multiple of the number of attention "
                f"heads ({config.num_attention_heads})"
            )

        self.num_attention_heads = config.num_attention_heads
        self.attn_head_size = int(config.hidden_size / config.num_attention_heads)
        self.hidden_size = config.hidden_size
        self.p_dropout = config.attention_probs_dropout_prob

        # key, query, value projections for all heads, but in a batch
        self.Wqkv = nn.Linear(config.hidden_size, 3 * config.hidden_size, bias=config.attn_qkv_bias)

        # biases projections for all heads, but in a batch
        self.h_attn = nn.Linear(config.hidden_size, config.num_attention_heads)

        # output projection
        self.Wo = nn.Linear(config.hidden_size, config.hidden_size, bias=config.attn_out_bias)
        self.out_drop = (
            nn.Dropout(config.attn_out_dropout_prob) if config.attn_out_dropout_prob > 0.0 else nn.Identity()
        )

        self.block_size = config.max_position_embeddings
        self.dtype = torch.float32  # Use consistent dtype

    def _init_weights(self, reset_params: bool = False):
        init_weights(
            self.config,
            self.Wqkv,
            layer_dim=self.config.hidden_size,
            layer_id=None,
            type_of_module=ModuleType.in_module,
        )
        init_weights(
            self.config,
            self.h_attn,
            layer_dim=self.config.hidden_size,
            layer_id=None,
            type_of_module=ModuleType.in_module,
        )
        init_weights(
            self.config,
            self.Wo,
            layer_dim=self.config.hidden_size,
            layer_id=self.layer_id,
            type_of_module=ModuleType.out_module,
        )

    def extra_repr(self) -> str:
        repr = ""
        if hasattr(self, "num_attention_heads"):
            repr += f"num_attention_heads={self.num_attention_heads}"
        if hasattr(self, "attn_head_size"):
            repr += f", attn_head_size={self.attn_head_size}"
        return repr

    def forward(
        self,
        hidden_states: torch.Tensor,
        cu_seqlens: torch.Tensor,
        max_seqlen: int,
        indices: Optional[torch.Tensor] = None,
        attn_mask: Optional[torch.Tensor] = None,
    ) -> torch.Tensor:
        """Perform self-attention with CABLE on unpadded sequences.

        Args:
            hidden_states: (total_nnz, dim)
            cu_seqlens: (batch + 1,)
            max_seqlen: int
            indices: (total_nnz,)
            attn_mask: (batch, max_seqlen)

        Returns:
            attention: (total_nnz, dim)
        """
        total_nnz, dim = hidden_states.size()
        batch_size = cu_seqlens.size(0) - 1

        # Extract query, key, value vectors
        qkv = self.Wqkv(hidden_states)
        q, k, v = qkv.split(self.hidden_size, dim=1)

        # We need to repad the sequences to apply CABLE
        # This is a limitation of the current implementation
        # Use a more memory-efficient approach with empty tensors and cat
        padded_hidden_states = torch.zeros(
            (batch_size, max_seqlen, dim),
            device=hidden_states.device,
            dtype=hidden_states.dtype
        )

        # Repad the sequences with bounds checking
        for i in range(batch_size):
            start_idx = cu_seqlens[i]
            end_idx = cu_seqlens[i + 1]
            seq_len = end_idx - start_idx
            if seq_len > 0 and seq_len <= max_seqlen:  # Add bounds checking
                padded_hidden_states[i, :seq_len] = hidden_states[start_idx:end_idx]

        # Compute biases on padded sequences
        b = torch.cumsum(
            -F.softplus(self.h_attn(padded_hidden_states)),
            dim=1
        ).permute(0, 2, 1)  # (B, nh, T)

        # Reshape q, k, v for attention computation
        padded_q = torch.zeros(
            (batch_size, max_seqlen, dim),
            device=q.device,
            dtype=q.dtype
        )
        padded_k = torch.zeros(
            (batch_size, max_seqlen, dim),
            device=k.device,
            dtype=k.dtype
        )
        padded_v = torch.zeros(
            (batch_size, max_seqlen, dim),
            device=v.device,
            dtype=v.dtype
        )

        # Repad q, k, v with bounds checking
        for i in range(batch_size):
            start_idx = cu_seqlens[i]
            end_idx = cu_seqlens[i + 1]
            seq_len = end_idx - start_idx
            if seq_len > 0 and seq_len <= max_seqlen:  # Add bounds checking
                padded_q[i, :seq_len] = q[start_idx:end_idx]
                padded_k[i, :seq_len] = k[start_idx:end_idx]
                padded_v[i, :seq_len] = v[start_idx:end_idx]

        # Reshape for attention
        padded_q = padded_q.view(batch_size, max_seqlen, self.num_attention_heads, dim // self.num_attention_heads).transpose(1, 2)
        padded_k = padded_k.view(batch_size, max_seqlen, self.num_attention_heads, dim // self.num_attention_heads).transpose(1, 2)
        padded_v = padded_v.view(batch_size, max_seqlen, self.num_attention_heads, dim // self.num_attention_heads).transpose(1, 2)

        # Calculate biases - no causal mask for bidirectional attention
        # Ensure biases are always negative by taking the negative absolute difference
        # Bias = (-1) * torch.abs(b.unsqueeze(3) - b.unsqueeze(2))  # (B, nh, T, T)
        Bias = (-1) * torch.log((b.unsqueeze(3) - b.unsqueeze(2))**2 + 1 )  # (B, nh, T, T)

        # Create attention mask from .quence lengths
        if attn_mask is None:
            attn_mask = torch.zeros((batch_size, max_seqlen), device=hidden_states.device, dtype=torch.bool)
            for i in range(batch_size):
                seq_len = cu_seqlens[i + 1] - cu_seqlens[i]
                attn_mask[i, :seq_len] = True

        # Using torch.baddbmm for faster matrix mult with bias
        b_shape = Bias.shape
        bt = Bias.reshape(b_shape[0] * b_shape[1], b_shape[2], b_shape[3])

        q_shape = padded_q.shape
        qt = padded_q.reshape(q_shape[0] * q_shape[1], q_shape[2], q_shape[3])
        k_shape = padded_k.shape
        kt = padded_k.reshape(k_shape[0] * k_shape[1], k_shape[2], k_shape[3])
        kt = kt.transpose(1, 2)

        # Apply attention mask
        # Ensure mask is boolean type and handle None case
        if attn_mask is not None:
            if attn_mask.dtype != torch.bool:
                attn_mask = attn_mask.to(torch.bool)

            expanded_mask = attn_mask[:, None, None, :].expand(batch_size, self.num_attention_heads, max_seqlen, max_seqlen)
            expanded_mask = expanded_mask & expanded_mask.transpose(-1, -2)  # Make sure both tokens are valid
            expanded_mask = expanded_mask.reshape(batch_size * self.num_attention_heads, max_seqlen, max_seqlen)
            bt = bt.masked_fill(~expanded_mask, float('-inf'))

        # Use different approaches for training and evaluation
        scale = 1.0 / math.sqrt(dim/self.num_attention_heads)

        qt_scaled = qt * scale

        try:
            # baddbmm: beta * input + alpha * (batch1 @ batch2)
            # Here beta=1, input=bt, alpha=1, batch1=qt_scaled, batch2=kt
            att_scores = torch.baddbmm(bt, qt_scaled, kt)
            att_scores = att_scores.reshape(batch_size, self.num_attention_heads, max_seqlen, max_seqlen)
        except RuntimeError as e:
            # Fallback to standard matmul if baddbmm fails
            qk = torch.matmul(qt_scaled, kt)  # [B*nh, T, T]
            att_scores = qk + bt
            att_scores = att_scores.reshape(batch_size, self.num_attention_heads, max_seqlen, max_seqlen)

        # Apply softmax and dropout with numerical stability
        if not self.training:
            # More stable approach for evaluation
            # First normalize to prevent overflow
            att_scores = att_scores - att_scores.max(dim=-1, keepdim=True)[0]
            # Apply softmax with extra stability measures
            exp_scores = torch.exp(att_scores)
            y = exp_scores / (exp_scores.sum(dim=-1, keepdim=True) + 1e-10)
            # No dropout during evaluation
        else:
            # Standard approach for training
            y = F.softmax(att_scores, dim=-1)
            y = F.dropout(y, p=self.p_dropout, training=self.training)

        # Apply attention to values
        y = y @ padded_v

        # Reshape back
        y = y.transpose(1, 2).contiguous().view(batch_size, max_seqlen, dim)

        # Unpad the result with bounds checking
        result = torch.zeros_like(hidden_states)
        for i in range(batch_size):
            start_idx = cu_seqlens[i]
            end_idx = cu_seqlens[i + 1]
            seq_len = end_idx - start_idx
            if seq_len > 0 and seq_len <= max_seqlen:  # Add bounds checking
                result[start_idx:end_idx] = y[i, :seq_len]

        # Output projection
        result = self.out_drop(self.Wo(result))

        return result
