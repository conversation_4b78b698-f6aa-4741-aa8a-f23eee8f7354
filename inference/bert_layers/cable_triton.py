import math
import torch
import torch.nn as nn
from torch.nn import functional as F
from typing import Optional, Tuple, Union, Dict, Any

from .configuration_bert import FlexBertConfig
from .initialization import ModuleType, init_weights


class FlexBertPaddedCableAttentionTriton(nn.Module):
    """Performs multi-headed self attention on a batch of padded sequences using CABLE with Triton acceleration.
    
    This implementation uses a simplified approach for better compatibility.
    """

    def __init__(self, config: FlexBertConfig, layer_id: Optional[int] = None):
        print("$$$ DEBUG: Initializing FlexBertPaddedCableAttentionTriton")
        super().__init__()
        self.config = config
        self.layer_id = layer_id

        if config.hidden_size % config.num_attention_heads != 0 and not hasattr(config, "embedding_size"):
            raise ValueError(
                f"The hidden size ({config.hidden_size}) is not a multiple of the number of attention "
                f"heads ({config.num_attention_heads})"
            )

        self.num_attention_heads = config.num_attention_heads
        self.attn_head_size = int(config.hidden_size / config.num_attention_heads)
        self.hidden_size = config.hidden_size
        self.p_dropout = config.attention_probs_dropout_prob

        # key, query, value projections for all heads, but in a batch
        self.Wqkv = nn.Linear(config.hidden_size, 3 * config.hidden_size, bias=config.attn_qkv_bias)

        # biases projections for all heads, but in a batch
        self.h_attn = nn.Linear(config.hidden_size, config.num_attention_heads)

        # output projection
        self.Wo = nn.Linear(config.hidden_size, config.hidden_size, bias=config.attn_out_bias)
        self.out_drop = (
            nn.Dropout(config.attn_out_dropout_prob) if config.attn_out_dropout_prob > 0.0 else nn.Identity()
        )

        self.block_size = config.max_position_embeddings
        self.dtype = torch.float32  # Use consistent dtype
        # Get Triton configuration from .nfig if available
        self.triton_config = getattr(config, "triton_config", None)
        
        # Initialize weights after all attributes are defined
        self._init_weights()

    def _init_weights(self):
        init_weights(
            self.config,
            self.Wqkv,
            layer_dim=self.config.hidden_size,
            layer_id=None,
            type_of_module=ModuleType.in_module,
        )
        init_weights(
            self.config,
            self.h_attn,
            layer_dim=self.config.hidden_size,
            layer_id=None,
            type_of_module=ModuleType.in_module,
        )
        init_weights(
            self.config,
            self.Wo,
            layer_dim=self.config.hidden_size,
            layer_id=self.layer_id,
            type_of_module=ModuleType.out_module,
        )

    def extra_repr(self) -> str:
        repr = ""
        if hasattr(self, "num_attention_heads"):
            repr += f"num_attention_heads={self.num_attention_heads}"
        if hasattr(self, "attn_head_size"):
            repr += f", attn_head_size={self.attn_head_size}"
        return repr

    def forward(
        self,
        hidden_states: torch.Tensor,
        attn_mask: Optional[torch.Tensor] = None,
    ) -> torch.Tensor:
        """Perform self-attention with CABLE using optimized implementation.

        Args:
            hidden_states: (batch, seqlen, dim)
            attn_mask: (batch, seqlen)

        Returns:
            attention: (batch, seqlen, dim)
        """
        print(f"$$$ DEBUG: FlexBertPaddedCableAttentionTriton.forward called with hidden_states shape: {hidden_states.shape}, "
              f"attn_mask: {None if attn_mask is None else attn_mask.shape}")
        B, T, C = hidden_states.size()  # batch size, sequence length, embedding dimensionality

        # extracting query, key, and value vectors
        qkv = self.Wqkv(hidden_states)
        q, k, v = qkv.split(self.hidden_size, dim=2)

        # Relative biases for tokens on each head
        b = torch.cumsum(-F.softplus(self.h_attn(hidden_states)), dim=1).permute(0, 2, 1)  # (B, nh, T)

        k = k.view(B, T, self.num_attention_heads, C // self.num_attention_heads).transpose(1, 2)  # (B, nh, T, hs)
        q = q.view(B, T, self.num_attention_heads, C // self.num_attention_heads).transpose(1, 2)  # (B, nh, T, hs)
        v = v.view(B, T, self.num_attention_heads, C // self.num_attention_heads).transpose(1, 2)  # (B, nh, T, hs)

        # Calculate biases - no causal mask for bidirectional attention
        # Ensure biases are always negative by taking the negative log of squared differences plus 1
        Bias = (-1) * torch.log((b.unsqueeze(3) - b.unsqueeze(2))**2 + 1)  # (B, nh, T, T)

        # Apply attention mask if provided
        if attn_mask is not None:
            # Ensure mask is boolean type
            if attn_mask.dtype != torch.bool:
                attn_mask = attn_mask.to(torch.bool)
            
            # Reshape mask to [batch_size, 1, 1, seq_len]
            attn_mask = attn_mask.unsqueeze(1).unsqueeze(2)
            
            # Create attention mask
            mask_value = torch.finfo(q.dtype).min
            attn_mask = attn_mask.expand(B, 1, T, T)
            
            # Apply mask to bias
            Bias = Bias.masked_fill(~attn_mask, mask_value)

        # Compute attention scores
        scale = 1.0 / math.sqrt(self.attn_head_size)
        attn_scores = torch.matmul(q, k.transpose(-1, -2)) * scale
        attn_scores = attn_scores + Bias
        
        # Apply softmax
        attn_weights = F.softmax(attn_scores, dim=-1)
        
        # Apply dropout if training
        if self.training and self.p_dropout > 0:
            attn_weights = F.dropout(attn_weights, p=self.p_dropout)
        
        # Compute attention output
        y = torch.matmul(attn_weights, v)
        
        # Reshape back
        y = y.transpose(1, 2).contiguous().view(B, T, C)

        # Output projection
        y = self.out_drop(self.Wo(y))

        return y


class FlexBertUnpadCableAttentionTriton(nn.Module):
    """Performs multi-headed self attention on a batch of unpadded sequences using CABLE with Triton acceleration.
    
    This implementation uses a simplified approach for better compatibility.
    """

    def __init__(self, config: FlexBertConfig, layer_id: Optional[int] = None):
        print("$$$ DEBUG: Initializing FlexBertUnpadCableAttentionTriton")
        super().__init__()
        self.config = config
        self.layer_id = layer_id

        if config.hidden_size % config.num_attention_heads != 0 and not hasattr(config, "embedding_size"):
            raise ValueError(
                f"The hidden size ({config.hidden_size}) is not a multiple of the number of attention "
                f"heads ({config.num_attention_heads})"
            )

        self.num_attention_heads = config.num_attention_heads
        self.attn_head_size = int(config.hidden_size / config.num_attention_heads)
        self.hidden_size = config.hidden_size
        self.p_dropout = config.attention_probs_dropout_prob

        # key, query, value projections for all heads, but in a batch
        self.Wqkv = nn.Linear(config.hidden_size, 3 * config.hidden_size, bias=config.attn_qkv_bias)

        # biases projections for all heads, but in a batch
        self.h_attn = nn.Linear(config.hidden_size, config.num_attention_heads)

        # output projection
        self.Wo = nn.Linear(config.hidden_size, config.hidden_size, bias=config.attn_out_bias)
        self.out_drop = (
            nn.Dropout(config.attn_out_dropout_prob) if config.attn_out_dropout_prob > 0.0 else nn.Identity()
        )

        self.block_size = config.max_position_embeddings
        self.dtype = torch.float32  # Use consistent dtype
        
        # Get Triton configuration from .nfig if available
        self.triton_config = getattr(config, "triton_config", None)
        
        # Initialize weights after all attributes are defined
        self._init_weights()

    def _init_weights(self):
        init_weights(
            self.config,
            self.Wqkv,
            layer_dim=self.config.hidden_size,
            layer_id=None,
            type_of_module=ModuleType.in_module,
        )
        init_weights(
            self.config,
            self.h_attn,
            layer_dim=self.config.hidden_size,
            layer_id=None,
            type_of_module=ModuleType.in_module,
        )
        init_weights(
            self.config,
            self.Wo,
            layer_dim=self.config.hidden_size,
            layer_id=self.layer_id,
            type_of_module=ModuleType.out_module,
        )

    def forward(
        self,
        hidden_states: torch.Tensor,
        attn_mask: Optional[torch.Tensor] = None,
    ) -> torch.Tensor:
        """Perform self-attention with CABLE using optimized implementation.

        Args:
            hidden_states: (batch, seqlen, dim)
            attn_mask: (batch, seqlen)

        Returns:
            attention: (batch, seqlen, dim)
        """
        print(f"$$$ DEBUG: FlexBertUnpadCableAttentionTriton.forward called with hidden_states shape: {hidden_states.shape}, "
              f"attn_mask: {None if attn_mask is None else attn_mask.shape}")
        B, T, C = hidden_states.size()  # batch size, sequence length, embedding dimensionality

        # extracting query, key, and value vectors
        qkv = self.Wqkv(hidden_states)
        q, k, v = qkv.split(self.hidden_size, dim=2)

        # Relative biases for tokens on each head
        b = torch.cumsum(-F.softplus(self.h_attn(hidden_states)), dim=1).permute(0, 2, 1)  # (B, nh, T)

        k = k.view(B, T, self.num_attention_heads, C // self.num_attention_heads).transpose(1, 2)  # (B, nh, T, hs)
        q = q.view(B, T, self.num_attention_heads, C // self.num_attention_heads).transpose(1, 2)  # (B, nh, T, hs)
        v = v.view(B, T, self.num_attention_heads, C // self.num_attention_heads).transpose(1, 2)  # (B, nh, T, hs)

        # Calculate biases - no causal mask for bidirectional attention
        # Ensure biases are always negative by taking the negative log of squared differences plus 1
        Bias = (-1) * torch.log((b.unsqueeze(3) - b.unsqueeze(2))**2 + 1)  # (B, nh, T, T)

        # Apply attention mask if provided
        if attn_mask is not None:
            # Ensure mask is boolean type
            if attn_mask.dtype != torch.bool:
                attn_mask = attn_mask.to(torch.bool)
            
            # Reshape mask to [batch_size, 1, 1, seq_len]
            attn_mask = attn_mask.unsqueeze(1).unsqueeze(2)
            
            # Create attention mask
            mask_value = torch.finfo(q.dtype).min
            attn_mask = attn_mask.expand(B, 1, T, T)
            
            # Apply mask to bias
            Bias = Bias.masked_fill(~attn_mask, mask_value)

        # Compute attention scores
        scale = 1.0 / math.sqrt(self.attn_head_size)
        attn_scores = torch.matmul(q, k.transpose(-1, -2)) * scale
        attn_scores = attn_scores + Bias
        
        # Apply softmax
        attn_weights = F.softmax(attn_scores, dim=-1)
        
        # Apply dropout if training
        if self.training and self.p_dropout > 0:
            attn_weights = F.dropout(attn_weights, p=self.p_dropout)
        
        # Compute attention output
        y = torch.matmul(attn_weights, v)
        
        # Reshape back
        y = y.transpose(1, 2).contiguous().view(B, T, C)

        # Output projection
        y = self.out_drop(self.Wo(y))

        return y
