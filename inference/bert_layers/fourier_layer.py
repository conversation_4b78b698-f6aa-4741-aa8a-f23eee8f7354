# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

"""
Fourier Layer implementation for FlexBERT.

This module implements a Fourier transform layer that applies FFT, selects top-k frequencies,
and applies inverse FFT for frequency domain processing in transformer models.
"""

import torch
import torch.nn as nn
import torch.distributed
from typing import Optional
import warnings


class FlexBertFourierLayer(nn.Module):
    """
    Fourier Layer for frequency domain processing.
    
    This layer applies Fourier transform, keeps top h frequencies, and applies inverse Fourier transform.
    It can be used as an additional processing layer in transformer architectures to capture
    frequency domain patterns in the sequence.
    
    Key features:
    - FFT along sequence dimension
    - Top-k frequency selection based on magnitude
    - Inverse FFT reconstruction
    - Distributed training support
    """

    def __init__(self, config, fourier_frequencies: Optional[int] = None):
        super().__init__()
        self.config = config
        
        # Number of frequencies to keep (h parameter)
        if fourier_frequencies is not None:
            self.h = fourier_frequencies
        elif hasattr(config, 'rotali_fourier_frequencies'):
            self.h = config.rotali_fourier_frequencies
        elif hasattr(config, 'fourier_frequencies'):
            self.h = config.fourier_frequencies
        else:
            self.h = 100  # Default value
            
        self.debug_printed = False  # Flag to track if we've printed debug info

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of the Fourier layer.
        
        Args:
            x: Input tensor of shape (batch_size, seq_len, embedding_dim)
            
        Returns:
            x_reconstructed: Output tensor of same shape as input
        """
        # x shape: (batch_size, seq_len, embedding_dim)
        batch_size, seq_len, embedding_dim = x.shape

        # Print debug info only once per process
        if not self.debug_printed:
            try:
                if torch.distributed.is_initialized() and torch.distributed.get_rank() == 0:
                    print(f"FourierLayer input shape: {x.shape}, keeping top {self.h} frequencies")
                    self.debug_printed = True
            except:
                # If distributed is not initialized, just print from .y process
                print(f"FourierLayer input shape: {x.shape}, keeping top {self.h} frequencies")
                self.debug_printed = True

        try:
            # Apply FFT along sequence dimension (more natural for language)
            x_fft = torch.fft.rfft(x, dim=1)  # Real FFT along sequence dimension

            # Get the magnitude of the complex FFT values
            magnitudes = torch.abs(x_fft)  # (batch_size, freq_bins, embedding_dim)

            # Create a mask for the top h frequencies (global across embedding dimension)
            if self.h < magnitudes.shape[1]:
                # Compute average magnitude across embedding dimension
                avg_magnitudes = magnitudes.mean(dim=2)  # (batch_size, freq_bins)

                # Find the indices of the top h frequencies
                _, top_indices = torch.topk(avg_magnitudes, self.h, dim=1)  # (batch_size, h)

                # Create a mask of zeros
                mask = torch.zeros_like(x_fft, dtype=torch.bool)

                # Set the mask to True for the top h frequencies
                # Use advanced indexing for efficiency
                batch_indices = torch.arange(batch_size, device=x.device).unsqueeze(1)  # (batch_size, 1)
                mask[batch_indices, top_indices, :] = True

                # Apply the mask to keep only the top h frequencies
                x_fft_masked = torch.zeros_like(x_fft)
                x_fft_masked[mask] = x_fft[mask]
            else:
                # If h is larger than available frequencies, keep all
                x_fft_masked = x_fft

            # Apply inverse FFT to reconstruct the signal
            x_reconstructed = torch.fft.irfft(x_fft_masked, n=seq_len, dim=1)

            # Ensure output shape matches input shape exactly
            if x_reconstructed.shape != x.shape:
                # This can happen due to FFT/IFFT rounding, so we pad or truncate as needed
                if x_reconstructed.shape[1] < seq_len:
                    # Pad if too short
                    pad_size = seq_len - x_reconstructed.shape[1]
                    x_reconstructed = torch.nn.functional.pad(x_reconstructed, (0, 0, 0, pad_size))
                elif x_reconstructed.shape[1] > seq_len:
                    # Truncate if too long
                    x_reconstructed = x_reconstructed[:, :seq_len, :]

            return x_reconstructed

        except Exception as e:
            # Print error and return original input if something goes wrong
            if not self.debug_printed:
                try:
                    if torch.distributed.is_initialized() and torch.distributed.get_rank() == 0:
                        print(f"Error in FourierLayer: {e}")
                        self.debug_printed = True
                except:
                    # If distributed is not initialized, just print from .y process
                    print(f"Error in FourierLayer: {e}")
                    self.debug_printed = True
            
            # Return original input as fallback
            return x

    def extra_repr(self) -> str:
        """Return extra representation string for the module."""
        return f"fourier_frequencies={self.h}"


class FlexBertUnpadFourierLayer(nn.Module):
    """
    Fourier Layer for unpadded sequences.
    
    This version handles unpadded sequences by first padding them, applying the Fourier transform,
    and then unpadding the results. This ensures compatibility with the unpadded FlexBERT architecture.
    """

    def __init__(self, config, fourier_frequencies: Optional[int] = None):
        super().__init__()
        self.config = config
        
        # Number of frequencies to keep
        if fourier_frequencies is not None:
            self.h = fourier_frequencies
        elif hasattr(config, 'rotali_fourier_frequencies'):
            self.h = config.rotali_fourier_frequencies
        elif hasattr(config, 'fourier_frequencies'):
            self.h = config.fourier_frequencies
        else:
            self.h = 100
            
        self.debug_printed = False

    def forward(
        self, 
        x: torch.Tensor, 
        cu_seqlens: torch.Tensor, 
        max_seqlen: int, 
        indices: torch.Tensor
    ) -> torch.Tensor:
        """
        Forward pass for unpadded sequences.
        
        Args:
            x: Unpadded tensor of shape (total_nnz, embedding_dim)
            cu_seqlens: Cumulative sequence lengths
            max_seqlen: Maximum sequence length in the batch
            indices: Indices for unpadding
            
        Returns:
            x_reconstructed: Output tensor of same shape as input
        """
        try:
            import bert_padding
            
            # Pad the input
            x_padded = bert_padding.pad_input(x, indices, cu_seqlens.shape[0] - 1, max_seqlen)
            
            # Apply Fourier layer to padded input
            fourier_layer = FlexBertFourierLayer(self.config, self.h)
            x_fourier = fourier_layer(x_padded)
            
            # Unpad the result
            # Create a simple attention mask for unpadding
            batch_size = cu_seqlens.shape[0] - 1
            attn_mask = torch.zeros(batch_size, max_seqlen, device=x.device, dtype=torch.bool)
            for i in range(batch_size):
                seq_len = cu_seqlens[i + 1] - cu_seqlens[i]
                attn_mask[i, :seq_len] = True
            
            x_reconstructed = bert_padding.unpad_input_only(x_fourier, attn_mask)
            
            return x_reconstructed
            
        except Exception as e:
            if not self.debug_printed:
                print(f"Error in UnpadFourierLayer: {e}")
                self.debug_printed = True
            return x

    def extra_repr(self) -> str:
        """Return extra representation string for the module."""
        return f"fourier_frequencies={self.h}, unpadded=True"
