# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
import torch
import torch.nn as nn
import logging

# Add project root to path for imports

from .bert_layers.configuration_bert import FlexBertConfig
from .bert_layers.model import FlexBertForMaskedLM

# Handle imports - try relative first, then absolute
try:
    from ..config.model_config import ModelConfig
    from ..config.inference_config import InferenceConfig
    from .checkpoint_manager import CheckpointManager
except ImportError:
    # If relative imports fail, try direct module imports
    inference_dir = Path(__file__).parent.parent
    sys.path.insert(0, str(inference_dir))
    
    from config.model_config import ModelConfig
    from config.inference_config import InferenceConfig
    from core.checkpoint_manager import CheckpointManager

logger = logging.getLogger(__name__)


class ModelFactory:
    """
    Factory class for creating and configuring FlexBERT models for inference.
    
    Handles:
    - Model instantiation from configuration
    - Checkpoint loading and validation
    - Device and precision management
    - Configuration compatibility checks
    """
    
    def __init__(self, model_config: ModelConfig, inference_config: InferenceConfig):
        """
        Initialize ModelFactory.
        
        Args:
            model_config: Model configuration from training YAML
            inference_config: Inference-specific configuration
        """
        self.model_config = model_config
        self.inference_config = inference_config
        self.device = self._setup_device()
        
        logger.info(f"ModelFactory initialized for device: {self.device}")
    
    def _setup_device(self) -> torch.device:
        """Setup and validate the target device."""
        device_str = self.inference_config.device
        
        if device_str == "auto":
            if torch.cuda.is_available():
                device = torch.device("cuda")
                logger.info(f"Auto-selected CUDA device: {torch.cuda.get_device_name()}")
            else:
                device = torch.device("cpu")
                logger.info("Auto-selected CPU device (CUDA not available)")
        else:
            device = torch.device(device_str)
            logger.info(f"Using specified device: {device}")
        
        return device
    
    def create_model(self, checkpoint_path: Optional[str] = None) -> FlexBertForMaskedLM:
        """
        Create and load FlexBERT model for inference.
        
        Args:
            checkpoint_path: Optional path to model checkpoint
            
        Returns:
            Loaded FlexBertForMaskedLM model ready for inference
        """
        logger.info("Creating FlexBERT model...")
        
        # Create model configuration
        bert_config = self._create_bert_config()
        
        # Instantiate model
        model = FlexBertForMaskedLM(bert_config)
        
        # Load checkpoint if provided
        if checkpoint_path:
            self._load_checkpoint(model, checkpoint_path)
        
        # Move to target device
        model = model.to(self.device)
        
        # Set precision
        model = self._apply_precision(model)
        
        # Set to evaluation mode
        model.eval()
        
        logger.info(f"Model created successfully on {self.device}")
        return model
    
    def _create_bert_config(self) -> FlexBertConfig:
        """Create FlexBertConfig from model configuration."""
        config_dict = self.model_config.get_model_config_dict()
        
        # Add inference-specific overrides
        config_dict.update({
            'output_hidden_states': self.inference_config.return_hidden_states,
            'use_cache': self.inference_config.use_cache,
        })
        
        bert_config = FlexBertConfig(**config_dict)
        
        logger.debug(f"Created FlexBertConfig with {bert_config.num_hidden_layers} layers")
        return bert_config
    
    def _load_checkpoint(self, model: FlexBertForMaskedLM, checkpoint_path: str):
        """Load model weights from checkpoint."""
        logger.info(f"Loading checkpoint from: {checkpoint_path}")
        
        checkpoint_manager = CheckpointManager(checkpoint_path)
        state_dict, format_type = checkpoint_manager.load_checkpoint()
        
        # Clean up state dict keys if needed
        if any(key.startswith("model.") for key in state_dict.keys()):
            state_dict = checkpoint_manager.clean_state_dict_keys(state_dict, "model.")
            logger.info("Cleaned state dict keys by removing model. prefix")
        logger.info(f"Loaded checkpoint format: {format_type}")
        
        # Load state dict into model
        missing_keys, unexpected_keys = model.load_state_dict(state_dict, strict=False)
        
        if missing_keys:
            logger.warning(f"Missing keys in checkpoint: {missing_keys[:5]}...")
        if unexpected_keys:
            logger.warning(f"Unexpected keys in checkpoint: {unexpected_keys[:5]}...")
        
        logger.info("Checkpoint loaded successfully")
    
    def _apply_precision(self, model: FlexBertForMaskedLM) -> FlexBertForMaskedLM:
        """Apply the specified precision to the model."""
        precision = self.inference_config.precision
        
        if precision == "fp16":
            model = model.half()
            logger.info("Applied FP16 precision")
        elif precision == "bf16" and torch.cuda.is_available():
            model = model.to(dtype=torch.bfloat16)
            logger.info("Applied BF16 precision")
        elif precision == "fp32":
            model = model.float()
            logger.debug("Using FP32 precision")
        else:
            logger.warning(f"Unknown precision '{precision}', using FP32")
            model = model.float()
        
        return model
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the model configuration.
        
        Returns:
            Dictionary with model information
        """
        return {
            "model_name": self.model_config.model_name,
            "hidden_size": self.model_config.hidden_size,
            "num_hidden_layers": self.model_config.num_hidden_layers,
            "num_attention_heads": self.model_config.num_attention_heads,
            "is_unpadded": self.model_config.is_unpadded,
            "attention_layer": self.model_config.attention_layer,
            "device": str(self.device),
            "precision": self.inference_config.precision,
            "parameter_count": self._estimate_parameter_count(),
        }
    
    def _estimate_parameter_count(self) -> int:
        """Estimate the number of parameters in the model."""
        # Rough estimation based on model configuration
        hidden_size = self.model_config.hidden_size
        num_layers = self.model_config.num_hidden_layers
        vocab_size = self.model_config.vocab_size
        intermediate_size = self.model_config.intermediate_size
        
        # Embedding parameters
        embed_params = vocab_size * hidden_size
        
        # Attention parameters per layer
        attn_params_per_layer = (
            4 * hidden_size * hidden_size +  # Q, K, V, O projections
            hidden_size  # Layer norm
        )
        
        # MLP parameters per layer
        mlp_params_per_layer = (
            2 * hidden_size * intermediate_size +  # Up and down projections
            hidden_size  # Layer norm
        )
        
        # Total layer parameters
        layer_params = num_layers * (attn_params_per_layer + mlp_params_per_layer)
        
        # Output head
        head_params = vocab_size * hidden_size
        
        total_params = embed_params + layer_params + head_params
        
        return int(total_params)
    
    def validate_model_config_compatibility(self, checkpoint_path: str) -> bool:
        """
        Validate that model configuration is compatible with checkpoint.
        
        Args:
            checkpoint_path: Path to checkpoint file
            
        Returns:
            True if compatible, False otherwise
        """
        try:
            checkpoint_manager = CheckpointManager(checkpoint_path)
            checkpoint_info = checkpoint_manager.get_checkpoint_info()
            
            # Basic compatibility checks
            if "model" not in checkpoint_info:
                logger.error("Checkpoint does not contain model state")
                return False
            
            # Additional checks could be added here
            logger.info("Model configuration appears compatible with checkpoint")
            return True
            
        except Exception as e:
            logger.error(f"Failed to validate compatibility: {e}")
            return False 