#!/usr/bin/env python3
# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

"""
Example script demonstrating MLM (Masked Language Modeling) inference with ModernBERT.

This script shows how to:
1. Load a trained ModernBERT model from config and checkpoint
2. Perform mask-fill inference
3. Handle both padded and unpadded configurations automatically
4. Use different sampling strategies
"""

import sys
from pathlib import Path

# Add inference directory to path

from inference import ModernBERTInference
import json


def main():
    # Example paths - update these to your actual paths
    config_path = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
    checkpoint_path = "ckpt/your-checkpoint/latest-rank0.pt"
    
    print("Initializing ModernBERT Inference for MLM...")
    
    # Initialize inference with automatic padding/unpadding detection
    try:
        with ModernBERTInference(
            config_path=config_path,
            checkpoint_path=checkpoint_path,
            device="auto",
            precision="fp32"
        ) as inference:
            
            # Get model info
            model_info = inference.get_model_info()
            print(f"Model loaded: {model_info['parameter_count']:,} parameters")
            print(f"Padding configuration: {model_info['is_unpadded']}")
            
            # Example 1: Single mask prediction
            print("\n=== Example 1: Single Mask Prediction ===")
            text = "The capital of France is [MASK]."
            predictions = inference.predict_masked_tokens(text, top_k=5)
            
            print(f"Input: {text}")
            print("Top predictions:")
            if "mask_predictions" in predictions:
                for mask_pred in predictions["mask_predictions"]:
                    for i, pred in enumerate(mask_pred["predictions"][:5]):
                        print(f"  {i+1}. {pred['token']} (probability: {pred['probability']:.4f})")
            
            # Example 2: Multiple masks in one sentence
            print("\n=== Example 2: Multiple Masks ===")
            text = "The [MASK] of [MASK] is Paris."
            predictions = inference.predict_masked_tokens(text, top_k=3)
            
            print(f"Input: {text}")
            if "mask_predictions" in predictions:
                for i, mask_pred in enumerate(predictions["mask_predictions"]):
                    print(f"Mask {i+1} predictions:")
                    for j, pred in enumerate(mask_pred["predictions"][:3]):
                        print(f"  {j+1}. {pred['token']} (probability: {pred['probability']:.4f})")
            
            # Example 3: Batch prediction
            print("\n=== Example 3: Batch Prediction ===")
            texts = [
                "The weather today is [MASK].",
                "I love to eat [MASK] for breakfast.",
                "Machine learning is a type of [MASK] intelligence."
            ]
            
            batch_predictions = inference.batch_predict_masked_tokens(texts, top_k=3)
            
            for i, (text, pred) in enumerate(zip(texts, batch_predictions)):
                print(f"\nBatch {i+1}: {text}")
                if "mask_predictions" in pred:
                    for mask_pred in pred["mask_predictions"]:
                        for j, p in enumerate(mask_pred["predictions"][:3]):
                            print(f"  {j+1}. {p['token']} (prob: {p['probability']:.4f})")
            
            # Example 4: HuggingFace pipeline compatible interface
            print("\n=== Example 4: HuggingFace Compatible Interface ===")
            text = "The best programming language is [MASK]."
            hf_results = inference.fill_mask(text, return_top_k=5)
            
            print(f"Input: {text}")
            print("HuggingFace style results:")
            for i, result in enumerate(hf_results):
                print(f"  {i+1}. {result['sequence']} (score: {result['score']:.4f})")
            
            # Example 5: Temperature sampling
            print("\n=== Example 5: Temperature Sampling ===")
            text = "The future of AI is [MASK]."
            
            print(f"Input: {text}")
            print("With different temperatures:")
            
            for temp in [0.5, 1.0, 1.5]:
                predictions = inference.predict_masked_tokens(
                    text, 
                    top_k=3, 
                    temperature=temp
                )
                print(f"\nTemperature {temp}:")
                if "mask_predictions" in predictions:
                    for mask_pred in predictions["mask_predictions"]:
                        for j, pred in enumerate(mask_pred["predictions"][:3]):
                            print(f"  {j+1}. {pred['token']} (prob: {pred['probability']:.4f})")
            
            # Example 6: Perplexity evaluation
            print("\n=== Example 6: Perplexity Evaluation ===")
            evaluation_texts = [
                "The sun rises in the east.",
                "Colorless green ideas sleep furiously.",  # Chomsky's famous nonsensical sentence
                "Machine learning models require large datasets."
            ]
            
            perplexity_results = inference.evaluate_perplexity(evaluation_texts)
            
            print("Perplexity scores (lower is better):")
            for result in perplexity_results:
                print(f"  Text: {result['text']}")
                print(f"  Perplexity: {result['perplexity']:.2f}")
                print(f"  Tokens evaluated: {result['num_tokens_evaluated']}/{result['total_tokens']}")
                print()
            
    except FileNotFoundError as e:
        print(f"Error: {e}")
        print("Please update the config_path and checkpoint_path variables with your actual file paths.")
    except Exception as e:
        print(f"An error occurred: {e}")
        raise


if __name__ == "__main__":
    main() 