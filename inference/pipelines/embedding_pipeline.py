# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

import os
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
import torch
import torch.nn.functional as F
import numpy as np
import logging

# Add project root to path for imports

# Handle imports - try relative first, then absolute
try:
    from .base_pipeline import BasePipeline
    from ..config.model_config import ModelConfig
    from ..config.inference_config import InferenceConfig
    from ..utils.padding_utils import PaddingUtils
except ImportError:
    # If relative imports fail, try direct module imports
    inference_dir = Path(__file__).parent.parent
    sys.path.insert(0, str(inference_dir))
    
    from pipelines.base_pipeline import BasePipeline
    from config.model_config import ModelConfig
    from config.inference_config import InferenceConfig
    from utils.padding_utils import PaddingUtils

logger = logging.getLogger(__name__)


class EmbeddingPipeline(BasePipeline):
    """
    Pipeline for generating sentence embeddings.
    
    Supports various pooling strategies and automatic adaptation to 
    padding/unpadding configuration based on the training setup.
    """
    
    def __init__(
        self,
        model_config: ModelConfig,
        inference_config: InferenceConfig,
        checkpoint_path: Optional[str] = None
    ):
        """
        Initialize Embedding Pipeline.
        
        Args:
            model_config: Model configuration
            inference_config: Inference configuration
            checkpoint_path: Optional path to model checkpoint
        """
        super().__init__(model_config, inference_config, checkpoint_path)
        logger.info("Embedding Pipeline initialized")
    
    def predict(
        self,
        inputs: Union[str, List[str]],
        pooling_strategy: Optional[str] = None,
        normalize: Optional[bool] = None,
        layer: Optional[int] = None
    ) -> Union[np.ndarray, List[np.ndarray]]:
        """
        Generate embeddings for input text(s).
        
        Args:
            inputs: Input text(s) to encode
            pooling_strategy: Pooling strategy ("mean", "cls", "max", "mean_sqrt_len")
            normalize: Whether to L2 normalize embeddings
            layer: Which layer to extract embeddings from (-1 for last layer)
            
        Returns:
            Embedding vectors as numpy arrays
        """
        # Handle single string input
        single_input = isinstance(inputs, str)
        if single_input:
            inputs = [inputs]
        
        # Use config defaults if not provided
        pooling_strategy = pooling_strategy or self.inference_config.embedding_pooling_strategy
        normalize = normalize if normalize is not None else self.inference_config.embedding_normalize
        layer = layer if layer is not None else self.inference_config.embedding_layer
        
        # Prepare model inputs
        model_inputs = self._prepare_inputs(inputs, task_type="embedding")
        
        # Run model inference
        hidden_states = self._extract_hidden_states(model_inputs, layer)
        
        # Pool embeddings based on padding configuration
        embeddings = self._pool_embeddings(
            hidden_states=hidden_states,
            model_inputs=model_inputs,
            pooling_strategy=pooling_strategy
        )
        
        # Normalize if requested
        if normalize:
            embeddings = F.normalize(embeddings, p=2, dim=-1)
        
        # Convert to numpy
        embeddings_np = embeddings.cpu().numpy()
        
        # Return single embedding if single input
        if single_input:
            return embeddings_np[0]
        
        return embeddings_np
    
    def encode(
        self,
        sentences: Union[str, List[str]],
        batch_size: Optional[int] = None,
        **kwargs
    ) -> np.ndarray:
        """
        Encode sentences to embeddings (similar to sentence-transformers interface).
        
        Args:
            sentences: Sentences to encode
            batch_size: Batch size for processing
            **kwargs: Additional encoding parameters
            
        Returns:
            Embedding matrix [num_sentences, embedding_dim]
        """
        if isinstance(sentences, str):
            sentences = [sentences]
        
        if batch_size is None:
            batch_size = self.inference_config.max_batch_size
        
        all_embeddings = []
        
        for i in range(0, len(sentences), batch_size):
            batch = sentences[i:i + batch_size]
            batch_embeddings = self.predict(batch, **kwargs)
            
            if isinstance(batch_embeddings, np.ndarray) and batch_embeddings.ndim == 1:
                # Single embedding
                all_embeddings.append(batch_embeddings[np.newaxis, :])
            else:
                all_embeddings.append(batch_embeddings)
        
        return np.vstack(all_embeddings)
    
    def similarity(
        self,
        sentences1: Union[str, List[str]],
        sentences2: Union[str, List[str]],
        similarity_fn: str = "cosine"
    ) -> Union[float, np.ndarray]:
        """
        Compute similarity between sentences.
        
        Args:
            sentences1: First set of sentences
            sentences2: Second set of sentences
            similarity_fn: Similarity function ("cosine", "dot", "euclidean")
            
        Returns:
            Similarity scores
        """
        # Encode sentences
        embeddings1 = self.encode(sentences1)
        embeddings2 = self.encode(sentences2)
        
        # Ensure 2D arrays
        if embeddings1.ndim == 1:
            embeddings1 = embeddings1[np.newaxis, :]
        if embeddings2.ndim == 1:
            embeddings2 = embeddings2[np.newaxis, :]
        
        # Compute similarity
        if similarity_fn == "cosine":
            # Normalize embeddings
            embeddings1_norm = embeddings1 / np.linalg.norm(embeddings1, axis=1, keepdims=True)
            embeddings2_norm = embeddings2 / np.linalg.norm(embeddings2, axis=1, keepdims=True)
            similarities = np.sum(embeddings1_norm * embeddings2_norm, axis=1)
        elif similarity_fn == "dot":
            similarities = np.sum(embeddings1 * embeddings2, axis=1)
        elif similarity_fn == "euclidean":
            similarities = -np.linalg.norm(embeddings1 - embeddings2, axis=1)
        else:
            raise ValueError(f"Unknown similarity function: {similarity_fn}")
        
        # Return scalar if single comparison
        if similarities.shape[0] == 1:
            return similarities[0]
        
        return similarities
    
    def _extract_hidden_states(
        self,
        model_inputs: Dict[str, torch.Tensor],
        layer: int
    ) -> torch.Tensor:
        """
        Extract hidden states from the specified layer.
        
        Args:
            model_inputs: Prepared model inputs
            layer: Layer index to extract from
            
        Returns:
            Hidden states tensor
        """
        # Get model outputs with hidden states
        model_inputs_copy = model_inputs.copy()
        
        # Temporarily override return_hidden_states
        original_return_hidden_states = self.inference_config.return_hidden_states
        self.inference_config.return_hidden_states = True
        
        try:
            # Run model - need to get the bert model output directly for embeddings
            with torch.no_grad():
                if self.model_config.is_unpadded:
                    # For unpadded inputs, call bert directly
                    outputs = self.model.bert(**model_inputs_copy)
                else:
                    # For padded inputs, can use regular forward
                    outputs = self.model.bert(**model_inputs_copy)
            
            # Extract hidden states
            if hasattr(outputs, 'hidden_states') and outputs.hidden_states is not None:
                # Use specified layer
                hidden_states = outputs.hidden_states[layer]
            else:
                # Use the main output (last layer)
                hidden_states = outputs
            
        finally:
            # Restore original setting
            self.inference_config.return_hidden_states = original_return_hidden_states
        
        return hidden_states
    
    def _pool_embeddings(
        self,
        hidden_states: torch.Tensor,
        model_inputs: Dict[str, torch.Tensor],
        pooling_strategy: str
    ) -> torch.Tensor:
        """
        Pool hidden states to create sentence embeddings.
        
        Args:
            hidden_states: Hidden states tensor
            model_inputs: Original model inputs
            pooling_strategy: Pooling strategy to use
            
        Returns:
            Pooled embeddings
        """
        if self.model_config.is_unpadded:
            return self._pool_unpadded_embeddings(hidden_states, model_inputs, pooling_strategy)
        else:
            return self._pool_padded_embeddings(hidden_states, model_inputs, pooling_strategy)
    
    def _pool_padded_embeddings(
        self,
        hidden_states: torch.Tensor,
        model_inputs: Dict[str, torch.Tensor],
        pooling_strategy: str
    ) -> torch.Tensor:
        """Pool embeddings from padded hidden states."""
        attention_mask = model_inputs["attention_mask"]
        
        if pooling_strategy == "cls":
            # Use CLS token (first token)
            embeddings = hidden_states[:, 0, :]
        
        elif pooling_strategy == "mean":
            # Mean pooling with attention mask
            input_mask_expanded = attention_mask.unsqueeze(-1).expand(hidden_states.size()).float()
            sum_embeddings = torch.sum(hidden_states * input_mask_expanded, 1)
            sum_mask = torch.sum(input_mask_expanded, 1)
            embeddings = sum_embeddings / torch.clamp(sum_mask, min=1e-9)
        
        elif pooling_strategy == "max":
            # Max pooling with attention mask
            input_mask_expanded = attention_mask.unsqueeze(-1).expand(hidden_states.size()).float()
            hidden_states[input_mask_expanded == 0] = -1e9  # Set padding tokens to large negative value
            embeddings = torch.max(hidden_states, 1)[0]
        
        elif pooling_strategy == "mean_sqrt_len":
            # Mean pooling normalized by sqrt of sequence length
            input_mask_expanded = attention_mask.unsqueeze(-1).expand(hidden_states.size()).float()
            sum_embeddings = torch.sum(hidden_states * input_mask_expanded, 1)
            seq_lengths = torch.sum(attention_mask, 1).float()
            embeddings = sum_embeddings / torch.sqrt(torch.clamp(seq_lengths.unsqueeze(-1), min=1e-9))
        
        else:
            raise ValueError(f"Unknown pooling strategy: {pooling_strategy}")
        
        return embeddings
    
    def _pool_unpadded_embeddings(
        self,
        hidden_states: torch.Tensor,
        model_inputs: Dict[str, torch.Tensor],
        pooling_strategy: str
    ) -> torch.Tensor:
        """Pool embeddings from unpadded hidden states."""
        cu_seqlens = model_inputs["cu_seqlens"]
        
        # Use the specialized unpadded pooling function
        embeddings = PaddingUtils.compute_unpadded_embeddings_pooling(
            hidden_states=hidden_states,
            cu_seqlens=cu_seqlens,
            pooling_strategy=pooling_strategy
        )
        
        return embeddings
    
    def get_embedding_dimension(self) -> int:
        """
        Get the dimension of embeddings produced by this pipeline.
        
        Returns:
            Embedding dimension
        """
        return self.model_config.hidden_size
    
    def compute_embeddings_for_clustering(
        self,
        texts: List[str],
        batch_size: Optional[int] = None,
        normalize: bool = True
    ) -> np.ndarray:
        """
        Compute embeddings optimized for clustering tasks.
        
        Args:
            texts: Input texts
            batch_size: Batch size for processing
            normalize: Whether to normalize embeddings
            
        Returns:
            Embedding matrix suitable for clustering
        """
        # Use mean pooling for clustering (generally works better than CLS)
        embeddings = self.encode(
            texts,
            batch_size=batch_size,
            pooling_strategy="mean",
            normalize=normalize
        )
        
        return embeddings
    
    def compute_embeddings_for_retrieval(
        self,
        texts: List[str],
        batch_size: Optional[int] = None
    ) -> np.ndarray:
        """
        Compute embeddings optimized for retrieval tasks.
        
        Args:
            texts: Input texts
            batch_size: Batch size for processing
            
        Returns:
            Embedding matrix suitable for retrieval
        """
        # Use normalized mean pooling for retrieval
        embeddings = self.encode(
            texts,
            batch_size=batch_size,
            pooling_strategy="mean",
            normalize=True
        )
        
        return embeddings 